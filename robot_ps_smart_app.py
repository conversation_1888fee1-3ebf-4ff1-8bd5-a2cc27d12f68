#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
机器人画布智能排版工具 - 经济版
创建一个2米宽的画布，从图库中加载图片并排列，保存为TIFF格式

依赖说明：
1. 基本依赖：pip install numpy pyqt6 pandas duckdb
2. Photoshop API依赖：pip install photoshop-python-api
   - 需要安装Adobe Photoshop软件
   - 支持Photoshop版本: CS3 ~ CC 2024
   - 详细说明：https://github.com/loonghao/photoshop-python-api

使用说明：
1. 本工具使用Photoshop进行图像处理，提供高质量的图像输出和专业功能
2. 必须安装Adobe Photoshop软件才能使用本工具
3. 使用高级JavaScript优化图像处理流程，提供更高效的图像排列和保存功能
"""

import os
import sys
import logging
import pandas as pd
from supabase import create_client, Client
from PyQt6.QtWidgets import (
    QApplication, QWidget, QVBoxLayout, QHBoxLayout, QPushButton,
    QLabel, QFileDialog, QProgressBar, QTextEdit, QSplitter,
    QGroupBox, QGridLayout, QCheckBox, QSpinBox, QComboBox,
    QMessageBox, QFrame, QScrollArea, QSizePolicy, QTabWidget,
    QDoubleSpinBox, QLineEdit, QStatusBar, QDialog, QMainWindow,
    QFormLayout, QDialogButtonBox
)
from PyQt6.QtCore import Qt, QTimer
from PyQt6.QtGui import QFont, QColor
from datetime import datetime
import re

# 导入自定义模块
from core.image_indexer_duckdb import ImageIndexerDuckDB
from core.excel_processor import ExcelProcessor
from utils.config_manager_duckdb import ConfigManagerDuckDB
from utils.constants import get_constant
from utils.photoshop_helper import PhotoshopHelper
from utils.supabase_helper import SupabaseHelper
from utils.advanced_settings_auth import AdvancedSettingsAuth
from utils.json_to_duckdb_migrator import migrate_config
from ui.settings_dialog import SettingsDialog
from ui.password_dialog import PasswordDialog
from ui.login_dialog import LoginDialog
# 已移除传统LayoutWorker，现在只使用RectPack
from ui.index_library_worker import IndexLibraryWorker
from ui.retrieve_images_worker import RetrieveImagesWorker

# 应用信息
APP_NAME = "DeAI-智能排版PhotoShop自动化"
APP_VERSION = "0.0.1"

# 配置日志
from utils.log_config import configure_logging, get_logger
configure_logging(level=logging.INFO)
log = get_logger("SmartLayoutApp")

class ImageLayoutApp(QMainWindow):
    """智能排版应用主窗口"""

    def add_log(self, message: str):
        """添加日志信息到日志文本区域"""
        try:
            # 获取当前时间
            current_time = datetime.now().strftime("%H:%M:%S")

            # 格式化日志消息
            formatted_message = f"[{current_time}] {message}"

            # 如果日志文本区域已初始化，直接添加
            if hasattr(self, 'log_text') and self.log_text:
                self.log_text.append(formatted_message)
                # 自动滚动到底部
                self.log_text.verticalScrollBar().setValue(
                    self.log_text.verticalScrollBar().maximum()
                )
            else:
                # 如果日志文本区域未初始化，添加到缓冲区
                if not hasattr(self, 'log_buffer'):
                    self.log_buffer = []
                self.log_buffer.append(formatted_message)

            # 也将日志信息输出到控制台
            log.info(message)

        except Exception as e:
            log.error(f"添加日志失败: {str(e)}")

    def flush_log_buffer(self):
        """将日志缓冲区的内容刷新到界面"""
        if not self.log_buffer or not self.log_text:
            return

        # 批量添加日志
        self.log_text.append("\n".join(self.log_buffer))

        # 清空缓冲区
        self.log_buffer = []

        # 滚动到底部
        self.log_text.verticalScrollBar().setValue(
            self.log_text.verticalScrollBar().maximum()
        )

    def __init__(self, supabase_helper=None):
        """初始化应用主窗口

        Args:
            supabase_helper: Supabase辅助类实例，如果为None则创建新实例
        """
        super().__init__()

        # 迁移JSON配置到DuckDB（如果需要）
        self._migrate_config_if_needed()

        # 初始化Supabase
        self.supabase_helper = supabase_helper if supabase_helper else SupabaseHelper()

        # 检查用户是否已登录
        if self.supabase_helper.is_authenticated():
            log.info("用户已登录，将使用已认证的客户端")
        else:
            log.warning("用户未登录或会话无效，可能无法获取完整配置")

        # 初始化配置管理器，传入相同的Supabase辅助类实例
        self.config_manager = ConfigManagerDuckDB(supabase_helper=self.supabase_helper)

        # 从 Supabase 同步配置，包括精确查询图案全称设置
        self.config_manager.sync_from_supabase()

        # 同步表格模式设置
        success = self.config_manager.sync_table_mode()
        if success:
            is_standard_mode = self.config_manager.get_table_mode()
            log.info(f"已同步表格模式设置: {'标准模式' if is_standard_mode else '自定义模式'}")

        # 同步模糊查询设置
        success = self.config_manager.sync_fuzzy_query()
        if success:
            is_fuzzy_query = self.config_manager.get_fuzzy_query()
            log.info(f"已同步模糊查询设置: {'开启' if is_fuzzy_query else '关闭'}")

        # 同步图库索引快速模式设置
        success = self.config_manager.sync_db_scan_fast()
        if success:
            is_db_scan_fast = self.config_manager.get_db_scan_fast()
            log.info(f"已同步图库索引快速模式设置: {'开启' if is_db_scan_fast else '关闭'}")

        # 默认启用RectPack算法（替换tetris算法）
        self.config_manager.set('use_rectpack_algorithm', True)

        # 优化RectPack算法配置
        self._optimize_rectpack_config()

        # 初始化RectPack恢复机制
        self._initialize_rectpack_recovery()

        # 初始化全局单位转换器
        self._initialize_unit_converter()

        log.info("RectPack算法已默认启用，替换所有tetris算法，并应用优化配置和恢复机制")

        # 同步测试模式设置
        success = self.config_manager.sync_test_mode_settings()
        if success:
            test_mode_settings = self.config_manager.get_test_mode_settings()
            log.info(f"已同步测试模式设置: 是否开启测试模式={test_mode_settings['is_test_mode']}")

        # 初始化高级设置授权管理器，传入相同的Supabase辅助类实例
        self.settings_auth = AdvancedSettingsAuth(self.supabase_helper)

        # 检查版本并更新标题
        self.check_version()

        # 初始化UI组件
        self.log_text = None  # 将在init_ui中初始化
        self.photoshop_status = QLabel()  # 初始化Photoshop状态标签

        # 初始化成员变量
        self.library_path = ""  # 初始化为空字符串而不是None
        self.material_folder_path = ""  # 初始化为空字符串
        self.material_tasks = []
        self.current_task = None

        # 从配置中获取图库索引快速模式设置
        is_db_scan_fast = self.config_manager.get_db_scan_fast()
        self.image_indexer = ImageIndexerDuckDB(fast_mode=is_db_scan_fast)  # 使用DuckDB版本的索引器，根据配置决定是否使用快速模式
        self.excel_processor = ExcelProcessor()  # 使用新的Excel处理器

        # 初始化工作线程变量

        # 初始化工作线程
        self.index_library_worker = None
        self.retrieve_images_worker = None
        self.layout_worker = None
        self._stop_requested = False

        # 创建日志缓冲区和定时器，用于批量更新日志
        self.log_buffer = []
        self.log_update_timer = QTimer()
        self.log_update_timer.timeout.connect(self.flush_log_buffer)
        self.log_update_timer.start(100)  # 每100毫秒更新一次日志

        # 导入常量
        from utils.constants import get_constant

        # 加载配置
        canvas_settings = self.config_manager.get_canvas_settings()
        # 不需要画布宽度设置，使用默认值2.0作为备用值
        self.canvas_width_m = get_constant('CANVAS_DEFAULT_CANVAS_WIDTH_M', 2.0)  # 默认值，会被从sheet名称中提取的宽度覆盖
        self.max_height_cm = canvas_settings.get('max_height_cm', get_constant('PS_MAX_CANVAS_HEIGHT_CM', 5000))
        self.ppi = canvas_settings.get('ppi', get_constant('PS_DEFAULT_PPI', 72))
        self.image_spacing_cm = canvas_settings.get('image_spacing_cm', get_constant('CANVAS_DEFAULT_IMAGE_SPACING_CM', 0.1))
        self.horizontal_expansion_cm = canvas_settings.get('horizontal_expansion_cm', get_constant('CANVAS_DEFAULT_HORIZONTAL_EXPANSION_CM', 0))

        # 初始化UI
        self.init_ui()

        # 加载上次会话
        self.load_last_session()

        # 检查是否处于测试模式
        test_mode_settings = self.config_manager.get_test_mode_settings()
        is_test_mode = test_mode_settings.get('is_test_mode', False)

        # 设置定时器，定期检查Photoshop状态（仅在非测试模式下）
        if not is_test_mode:
            self.ps_check_timer = QTimer(self)
            self.ps_check_timer.timeout.connect(self.check_photoshop_running)
            self.ps_check_timer.start(10000)  # 每10秒检查一次
            # 启动时自动检查Photoshop状态
            self.check_photoshop_running()
        else:
            # 测试模式下，不检查Photoshop状态
            self.photoshop_status.setText("测试模式：不检查Photoshop状态")
            self.photoshop_status.setStyleSheet("color: blue")
            self.start_ps_btn.setEnabled(False)
            log.info("测试模式下，不检查Photoshop状态")

    def _migrate_config_if_needed(self):
        """如果需要，迁移JSON配置到DuckDB"""
        json_file = "config.json"
        db_file = "config.db"

        if os.path.exists(json_file) and not os.path.exists(db_file):
            log.info("检测到JSON配置文件，开始迁移到DuckDB...")
            try:
                success = migrate_config(json_file, db_file)
                if success:
                    log.info("配置迁移成功")
                else:
                    log.error("配置迁移失败")
            except Exception as e:
                log.error(f"配置迁移异常: {str(e)}")

    def _optimize_rectpack_config(self):
        """优化RectPack算法配置，确保生产环境下的最佳性能"""
        try:
            # 检查rectpack库是否可用
            try:
                import rectpack
                rectpack_available = True
                self.add_log("✅ RectPack库检测成功")
            except ImportError as e:
                rectpack_available = False
                self.add_log(f"❌ RectPack库不可用: {str(e)}")
                return

            # 设置生产环境优化参数
            optimized_config = {
                # 基础算法参数 - 优化画布利用率
                'rectpack_rotation_enabled': True,  # 启用旋转以提高利用率
                'rectpack_sort_strategy': 0,  # 使用面积排序策略
                'rectpack_pack_algorithm': 0,  # 使用Best Short Side Fit算法

                # 高级算法参数 - 提升排列质量
                'rectpack_bin_selection_strategy': 2,  # 使用最佳bin选择策略
                'rectpack_split_heuristic': 1,  # 启用分割启发式
                'rectpack_free_rect_choice': 0,  # 使用最佳短边适应

                # 优化参数 - 平衡性能和质量
                'rectpack_enable_optimization': True,  # 启用利用率优化
                'rectpack_optimization_iterations': 3,  # 减少迭代次数以提升速度
                'rectpack_min_utilization_threshold': 80.0,  # 降低阈值以适应更多场景
                'rectpack_rotation_penalty': 0.02,  # 降低旋转惩罚
                'rectpack_aspect_ratio_preference': 1.2,  # 轻微偏好横向布局

                # 性能参数 - 确保稳定运行
                'rectpack_max_processing_time': 180,  # 3分钟超时
                'rectpack_batch_size': 50,  # 适中的批处理大小
                'rectpack_memory_limit_mb': 512,  # 512MB内存限制
                'rectpack_enable_parallel': False,  # 禁用并行以避免PS冲突

                # 调试参数 - 生产环境设置
                'rectpack_debug_mode': False,  # 关闭调试模式
                'rectpack_log_level': 1,  # 基础日志级别
                'rectpack_save_intermediate_results': False,  # 不保存中间结果
                'rectpack_visualization_enabled': False,  # 关闭可视化
            }

            # 应用优化配置
            for key, value in optimized_config.items():
                self.config_manager.set(key, value)

            self.add_log("✅ RectPack算法配置已优化，适用于生产环境")
            self.add_log(f"   - 旋转功能: 启用")
            self.add_log(f"   - 优化迭代: 3次")
            self.add_log(f"   - 利用率阈值: 80%")
            self.add_log(f"   - 内存限制: 512MB")
            self.add_log(f"   - 处理超时: 3分钟")

        except Exception as e:
            self.add_log(f"⚠️ RectPack配置优化失败: {str(e)}")
            log.error(f"RectPack配置优化失败: {str(e)}")

    def _initialize_rectpack_recovery(self):
        """初始化RectPack错误恢复机制"""
        try:
            from utils.rectpack_recovery import get_recovery_manager

            # 获取全局恢复管理器，传入日志信号
            self.rectpack_recovery = get_recovery_manager(self.add_log)

            # 设置恢复参数
            self.rectpack_recovery.max_retries = 3
            self.rectpack_recovery.fallback_enabled = True

            self.add_log("✅ RectPack错误恢复机制已初始化")
            self.add_log(f"   - 最大重试次数: {self.rectpack_recovery.max_retries}")
            self.add_log(f"   - 降级策略: {'启用' if self.rectpack_recovery.fallback_enabled else '禁用'}")

        except Exception as e:
            self.add_log(f"⚠️ RectPack恢复机制初始化失败: {str(e)}")
            log.error(f"RectPack恢复机制初始化失败: {str(e)}")
            # 不抛出异常，继续运行

    def _initialize_unit_converter(self):
        """初始化全局单位转换器，确保测试模式和生产模式统一处理"""
        try:
            from utils.unit_converter import initialize_from_config, get_global_ppi

            # 从配置管理器初始化单位转换器
            success = initialize_from_config(self.config_manager)

            if success:
                current_ppi = get_global_ppi()
                self.add_log("✅ 全局单位转换器已初始化")
                self.add_log(f"   - 当前PPI: {current_ppi}")
                self.add_log(f"   - 转换精度: 高精度浮点运算")
                self.add_log(f"   - 缓存机制: 已启用")

                # 检查测试模式设置
                test_mode_settings = self.config_manager.get_test_mode_settings()
                is_test_mode = test_mode_settings.get('is_test_mode', False)

                if is_test_mode:
                    self.add_log("   - 测试模式: cm直接转换为px，实现统一单位处理")
                    self.add_log("   - 测试模式: 容器最大高度将严格遵循配置限制")
                else:
                    self.add_log("   - 生产模式: 使用真实cm转px转换")

            else:
                self.add_log("⚠️ 单位转换器初始化失败，使用默认设置")

        except Exception as e:
            self.add_log(f"⚠️ 单位转换器初始化失败: {str(e)}")
            log.error(f"单位转换器初始化失败: {str(e)}")
            # 不抛出异常，继续运行

    def init_ui(self):
        """初始化UI界面"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        main_layout = QVBoxLayout(central_widget)

        # 创建顶部控制面板
        control_group = QGroupBox("控制面板")
        control_layout = QGridLayout(control_group)

        # 第一行：选择图库和选择材质表格文件夹
        self.library_label = QLabel("图库路径: 未选择")
        self.library_btn = QPushButton("选择图库")
        self.index_btn = QPushButton("索引图库")

        self.material_label = QLabel("材质表格文件夹: 未选择")
        self.material_btn = QPushButton("选择材质文件夹")

        # 根据模糊查询设置决定按钮文本
        is_fuzzy_query = self.config_manager.get_fuzzy_query()
        if is_fuzzy_query:
            self.load_and_layout_btn = QPushButton("图片排版")
        else:
            self.load_and_layout_btn = QPushButton("智能排版")

        # 添加图片索引按钮（初始隐藏，根据模糊查询设置决定是否显示）
        self.image_index_btn = QPushButton("图片索引")
        self.image_index_btn.setVisible(False)  # 默认隐藏

        # 设置智能排版按钮字体加粗
        font = QFont()
        font.setBold(True)
        self.load_and_layout_btn.setFont(font)

        control_layout.addWidget(self.library_label, 0, 0, 1, 2)
        control_layout.addWidget(self.library_btn, 0, 2)
        control_layout.addWidget(self.index_btn, 0, 3)

        control_layout.addWidget(self.material_label, 1, 0, 1, 2)
        control_layout.addWidget(self.material_btn, 1, 2)

        # 创建一个水平布局来容纳图片索引和智能排版按钮
        self.layout_buttons_container = QWidget()
        layout_buttons_layout = QHBoxLayout(self.layout_buttons_container)
        layout_buttons_layout.setContentsMargins(0, 0, 0, 0)
        layout_buttons_layout.setSpacing(5)

        # 根据模糊查询设置决定按钮布局
        is_fuzzy_query = self.config_manager.get_fuzzy_query()
        if is_fuzzy_query:
            # 如果开启模糊查询，显示两个按钮
            layout_buttons_layout.addWidget(self.image_index_btn)
            layout_buttons_layout.addWidget(self.load_and_layout_btn)
            self.image_index_btn.setVisible(True)
        else:
            # 如果关闭模糊查询，只显示智能排版按钮
            layout_buttons_layout.addWidget(self.load_and_layout_btn)
            self.image_index_btn.setVisible(False)

        control_layout.addWidget(self.layout_buttons_container, 1, 3)

        # 添加Photoshop状态标签
        self.photoshop_status = QLabel("Photoshop: 未检查")
        control_layout.addWidget(self.photoshop_status, 2, 0, 1, 2)

        # 添加Photoshop手动启动按钮
        self.start_ps_btn = QPushButton("启动Photoshop")
        self.start_ps_btn.clicked.connect(self.manual_start_photoshop)
        control_layout.addWidget(self.start_ps_btn, 2, 2)

        # 添加设置按钮
        self.settings_btn = QPushButton("高级设置")
        control_layout.addWidget(self.settings_btn, 2, 3)

        # 添加状态显示
        status_frame = QFrame()
        status_frame.setFrameShape(QFrame.Shape.StyledPanel)
        status_layout = QVBoxLayout(status_frame)

        # 创建高级进度指示器
        from ui.progress_indicator import ProgressIndicator
        self.progress_indicator = ProgressIndicator()

        # 添加进度阶段
        self.progress_indicator.add_stage("准备", 0.1, QColor(100, 100, 200))  # 浅蓝色
        self.progress_indicator.add_stage("索引", 0.2, QColor(0, 120, 215))    # 蓝色
        self.progress_indicator.add_stage("检索", 0.2, QColor(0, 180, 100))    # 绿色
        self.progress_indicator.add_stage("排列", 0.3, QColor(200, 100, 0))    # 橙色
        self.progress_indicator.add_stage("输出", 0.2, QColor(180, 0, 100))    # 紫色

        # 设置初始状态
        self.progress_indicator.set_title("就绪")
        self.progress_indicator.set_status("等待操作")

        # 添加进度指示器
        status_layout.addWidget(self.progress_indicator)

        # 创建状态布局
        status_info_layout = QHBoxLayout()

        # 保留旧的状态标签，用于兼容性
        self.status_label = QLabel("状态: 就绪")
        status_info_layout.addWidget(self.status_label)

        # 添加弹性空间
        status_info_layout.addStretch()

        # 添加状态信息布局
        status_layout.addLayout(status_info_layout)

        # 保留旧的进度条，用于兼容性
        self.progress_bar = QProgressBar()
        self.progress_bar.setRange(0, 100)
        self.progress_bar.setValue(0)
        self.progress_bar.setVisible(False)  # 隐藏旧的进度条

        # 添加状态框
        control_layout.addWidget(status_frame, 3, 0, 1, 4)

        # 添加日志区域
        log_group = QGroupBox("处理日志")
        log_layout = QVBoxLayout(log_group)

        self.log_text = QTextEdit()
        self.log_text.setReadOnly(True)
        self.log_text.setLineWrapMode(QTextEdit.LineWrapMode.NoWrap)  # 禁用自动换行
        self.log_text.setStyleSheet("""
            QTextEdit {
                font-family: Consolas, Monaco, monospace;
                font-size: 10pt;
                background-color: #f8f9fa;
                color: #212529;
            }
        """)
        log_layout.addWidget(self.log_text)

        # 添加控制面板和日志区域到主布局
        main_layout.addWidget(control_group)
        main_layout.addWidget(log_group)

        # 设置窗口属性
        self.resize(1000, 800)  # 增加窗口大小以显示更多日志

        # 连接信号
        self.library_btn.clicked.connect(self.select_library)
        self.index_btn.clicked.connect(self.index_library)
        self.material_btn.clicked.connect(self.select_material_folder)
        self.load_and_layout_btn.clicked.connect(self.retrieve_and_layout)
        self.image_index_btn.clicked.connect(self.image_index_only)  # 连接图片索引按钮
        self.settings_btn.clicked.connect(self.show_settings)
        self.start_ps_btn.clicked.connect(self.manual_start_photoshop)

        # 初始禁用部分按钮
        self.set_buttons_enabled()

        # 设置定时刷新密码
        QTimer.singleShot(10000, self.refresh_settings_password)

    def check_photoshop_running(self):
        """检查Photoshop是否正在运行"""
        # 检查是否处于测试模式
        test_mode_settings = self.config_manager.get_test_mode_settings()
        is_test_mode = test_mode_settings.get('is_test_mode', False)

        if is_test_mode:
            # 测试模式下，不检查Photoshop状态
            self.photoshop_status.setText("测试模式：不检查Photoshop状态")
            self.photoshop_status.setStyleSheet("color: blue")
            self.start_ps_btn.setEnabled(False)
            return

        # 非测试模式下，正常检查Photoshop状态
        success, message = PhotoshopHelper.check_photoshop()
        self.photoshop_status.setText(message)
        self.photoshop_status.setStyleSheet("color: green" if success else "color: red")
        self.start_ps_btn.setEnabled(not success)

    def manual_start_photoshop(self):
        """手动启动Photoshop"""
        # 检查是否处于测试模式
        test_mode_settings = self.config_manager.get_test_mode_settings()
        is_test_mode = test_mode_settings.get('is_test_mode', False)

        if is_test_mode:
            # 测试模式下，不启动Photoshop
            message = "测试模式下不需要启动Photoshop"
            self.status_label.setText(message)
            QMessageBox.information(self, "测试模式", message)
            return

        # 非测试模式下，正常启动Photoshop
        success, message = PhotoshopHelper.start_photoshop()
        if success:
            self.status_label.setText(message)
            QTimer.singleShot(5000, self.check_photoshop_running)
        else:
            QMessageBox.warning(self, "错误", message)

    def show_settings(self):
        """显示设置对话框，需要密码验证"""
        # 检查是否有密码设置
        if not self.settings_auth.has_password():
            QMessageBox.warning(
                self,
                "无法访问高级设置",
                "未配置高级设置密码，无法访问设置。\n请联系管理员设置密码。",
                QMessageBox.StandardButton.Ok
            )
            return

        # 显示密码验证对话框
        password_dialog = PasswordDialog(
            self.settings_auth._password,
            self,
            title="高级设置密码验证",
            prompt="请输入高级设置密码:",
            error_msg="密码验证失败，无法访问高级设置。"
        )
        password_entered = password_dialog.exec()

        if password_entered != QDialog.DialogCode.Accepted:
            self.add_log("用户取消了高级设置访问")
            return

        # 密码验证通过，显示设置对话框
        dialog = SettingsDialog(self.config_manager, self)
        if dialog.exec() == QDialog.DialogCode.Accepted:
            # 重新加载配置
            canvas_settings = self.config_manager.get_canvas_settings()
            # 不再需要canvas_width_m设置
            self.max_height_cm = canvas_settings.get('max_height_cm', get_constant('PS_MAX_CANVAS_HEIGHT_CM', 5000))
            self.ppi = canvas_settings.get('ppi', get_constant('PS_DEFAULT_PPI', 72))
            self.image_spacing_cm = canvas_settings.get('image_spacing_cm', get_constant('CANVAS_DEFAULT_IMAGE_SPACING_CM', 0.1))
            self.horizontal_expansion_cm = canvas_settings.get('horizontal_expansion_cm', get_constant('CANVAS_DEFAULT_HORIZONTAL_EXPANSION_CM', 0))

            # 获取测试模式设置
            test_mode_settings = self.config_manager.get_test_mode_settings()
            is_test_mode = test_mode_settings['is_test_mode']
            self.add_log(f"已更新高级设置，测试模式: {'开启' if is_test_mode else '关闭'}")

            # 检查模糊查询设置是否变更，如果变更则更新界面
            self.update_buttons_layout()

    def load_last_session(self):
        """加载上次会话的路径"""
        library_path, material_folder = self.config_manager.get_last_paths()

        if library_path and os.path.exists(library_path):
            self.library_path = library_path
            self.library_label.setText(f"图库路径: {library_path}")
            self.set_buttons_enabled()

        if material_folder and os.path.exists(material_folder):
            self.material_folder_path = material_folder
            self.material_label.setText(f"材质表格文件夹: {material_folder}")
            self.set_buttons_enabled()

    def select_library(self):
        """选择图库文件夹"""
        folder = QFileDialog.getExistingDirectory(
            self,
            "选择图库文件夹",
            self.library_path or os.path.expanduser("~")
        )

        if folder:
            self.library_path = folder
            self.library_label.setText(f"图库路径: {folder}")
            self.config_manager.save_last_paths(library_path=folder)
            self.set_buttons_enabled()

    def select_material_folder(self):
        """选择材质表格文件夹"""
        folder = QFileDialog.getExistingDirectory(
            self,
            "选择材质表格文件夹",
            self.material_folder_path or os.path.expanduser("~")
        )

        if folder:
            # 检查是否选择了新的文件夹
            is_new_folder = self.material_folder_path != folder

            # 更新材质文件夹路径
            self.material_folder_path = folder
            self.material_label.setText(f"材质表格文件夹: {folder}")
            self.config_manager.save_last_paths(material_folder=folder)

            # 如果选择了新的文件夹，重置任务状态
            if is_new_folder:
                # 清空之前的任务列表
                self.material_tasks = []
                # 重置进度条
                self.update_progress(0)
                # 更新状态标签
                self.status_label.setText("状态: 已重新选择材质表格文件夹，就绪")
                # 记录日志
                self.add_log(f"已重新选择材质表格文件夹: {folder}，可以重新开始排版任务")

            # 更新按钮状态
            self.set_buttons_enabled()

    def index_library(self):
        """索引图库，需要密码验证"""
        try:
            # 检查图库路径
            if not self.library_path or not os.path.exists(self.library_path):
                QMessageBox.warning(self, "警告", "请先选择图库文件夹！")
                return

            # 密码验证部分，与高级设置类似
            # 检查是否有密码设置
            if not self.settings_auth.has_password():
                QMessageBox.warning(
                    self,
                    "无法索引图库",
                    "未配置索引图库密码，无法进行索引。\n请联系管理员设置密码。",
                    QMessageBox.StandardButton.Ok
                )
                return

            # 显示密码验证对话框
            password_dialog = PasswordDialog(
                self.settings_auth._password,
                self,
                title="索引图库密码验证",
                prompt="请输入索引图库密码:",
                error_msg="密码验证失败，无法索引图库。"
            )
            password_entered = password_dialog.exec()

            if password_entered != QDialog.DialogCode.Accepted:
                self.add_log("用户取消了索引图库操作")
                return

            # 密码验证通过，继续索引操作
            self.add_log("密码验证通过，开始索引图库...")

            # 关闭现有数据库连接
            if self.image_indexer.db:
                try:
                    self.image_indexer.db.close()
                    self.image_indexer.db = None
                except:
                    pass

            # 删除.index文件夹
            index_dir = os.path.join(self.library_path, '.index')
            if os.path.exists(index_dir):
                try:
                    import shutil
                    shutil.rmtree(index_dir)
                except Exception as e:
                    self.add_log(f"删除索引文件夹失败: {str(e)}")
                    QMessageBox.warning(self, "错误", f"删除索引文件夹失败: {str(e)}")
                    return

            # 禁用按钮，防止重复操作
            self.index_btn.setEnabled(False)
            self.library_btn.setEnabled(False)

            # 更新状态
            self.status_label.setText("正在索引图库...")
            self.update_progress(0, stage_index=1)  # 使用索引阶段

            # 更新进度指示器
            self.progress_indicator.set_title("索引图库")
            self.progress_indicator.set_status("正在扫描图片文件...")

            # 创建并配置工作线程
            self.index_library_worker = IndexLibraryWorker()
            self.index_library_worker.set_library_path(self.library_path)

            # 从配置中获取图库索引快速模式设置
            is_db_scan_fast = self.config_manager.get_db_scan_fast()
            self.image_indexer.fast_mode = is_db_scan_fast

            if is_db_scan_fast:
                self.add_log("使用快速模式索引图库，仅获取文件路径和名称信息，不读取图片内容")
            else:
                self.add_log("使用完整模式索引图库，将读取图片内容获取尺寸和EXIF信息")

            self.index_library_worker.set_image_indexer(self.image_indexer)

            # 连接信号
            self.index_library_worker.progress_signal.connect(self.progress_bar.setValue)
            self.index_library_worker.log_signal.connect(self.add_log)
            self.index_library_worker.status_signal.connect(self.status_label.setText)
            self.index_library_worker.error_signal.connect(lambda msg: QMessageBox.warning(self, "错误", msg))
            self.index_library_worker.finished_signal.connect(self.on_index_library_finished)

            # 启动线程
            self.index_library_worker.start()
            self.add_log(f"开始索引图库: {self.library_path}")

        except Exception as e:
            self.add_log(f"索引图库时出错: {str(e)}")
            QMessageBox.warning(self, "错误", f"索引图库时出错: {str(e)}")

    def load_material_info(self, start_layout=True):
        """加载材质信息

        Args:
            start_layout: 是否在检索完成后自动开始排版，True表示自动开始排版，False表示仅执行图片索引
        """
        try:
            # 首先检查图库路径是否已设置
            if not self.library_path or not os.path.exists(self.library_path):
                self.add_log("错误: 未检索图库，请先选择图库路径并进行索引")
                QMessageBox.warning(self, "错误", "未检索图库，请先选择图库路径并进行索引")
                return

            # 设置是否自动开始排版的标志
            self._auto_start_layout = start_layout

            # 检查图库是否已索引
            if not self.image_indexer.is_indexed(self.library_path):
                self.add_log("错误: 图库尚未索引，请先索引图库")
                QMessageBox.warning(self, "错误", "图库尚未索引，请先索引图库")
                return

            # 检查材质文件夹路径
            if not self.material_folder_path or not os.path.exists(self.material_folder_path):
                self.add_log("错误: 请先选择材质表格文件夹")
                QMessageBox.warning(self, "错误", "请先选择材质表格文件夹")
                return

            # 禁用按钮，防止重复操作
            self.load_and_layout_btn.setEnabled(False)
            self.material_btn.setEnabled(False)

            # 更新状态
            self.status_label.setText("正在检索图片...")
            self.update_progress(0, stage_index=2)  # 使用检索阶段

            # 更新进度指示器
            self.progress_indicator.set_title("检索图片")
            self.progress_indicator.set_status("正在匹配图片...")

            # 创建并配置工作线程
            self.retrieve_images_worker = RetrieveImagesWorker()
            self.retrieve_images_worker.set_material_folder_path(self.material_folder_path)
            self.retrieve_images_worker.set_image_indexer(self.image_indexer)
            self.retrieve_images_worker.set_excel_processor(self.excel_processor)

            # 获取精确查询图案全称设置
            exact_pattern_search = self.config_manager.get('exact_pattern_search', False)
            self.retrieve_images_worker.set_exact_pattern_search(exact_pattern_search)
            if exact_pattern_search:
                self.add_log("精确查询图案全称模式已开启，仅查询'图案全称'字段")

            # 获取表格模式设置
            is_standard_mode = self.config_manager.get('is_standard_mode', True)
            self.retrieve_images_worker.set_table_mode(is_standard_mode)
            mode_str = "标准" if is_standard_mode else "自定义"
            self.add_log(f"使用{mode_str}表格模式处理Excel数据")

            # 获取模糊查询设置
            is_fuzzy_query = self.config_manager.get('is_fuzzy_query', False)
            self.retrieve_images_worker.set_fuzzy_query(is_fuzzy_query)
            if is_fuzzy_query:
                self.add_log("模糊查询模式已开启，可以查询部分匹配的结果")

            # 连接信号
            self.retrieve_images_worker.progress_signal.connect(self.progress_bar.setValue)
            self.retrieve_images_worker.log_signal.connect(self.add_log)
            self.retrieve_images_worker.status_signal.connect(self.status_label.setText)
            self.retrieve_images_worker.error_signal.connect(lambda msg: QMessageBox.warning(self, "错误", msg))
            self.retrieve_images_worker.finished_signal.connect(self.on_retrieve_images_finished)

            # 启动线程
            self.retrieve_images_worker.start()
            self.add_log(f"开始检索图片: {self.material_folder_path}")

            # 更新按钮状态
            self.set_buttons_enabled()

        except Exception as e:
            self.add_log(f"加载材质信息失败: {str(e)}")
            QMessageBox.warning(self, "错误", f"加载材质信息失败: {str(e)}")

    def start_layout(self):
        """开始布局处理"""
        try:
            # 检查是否有材质任务
            if not hasattr(self, 'material_tasks') or not self.material_tasks:
                # 如果没有材质任务，尝试从已检索的Excel文件加载
                is_fuzzy_query = self.config_manager.get_fuzzy_query()
                if is_fuzzy_query and hasattr(self, 'material_folder_path') and os.path.exists(self.material_folder_path):
                    # 查找所有已检索的Excel文件
                    indexed_files = []
                    for file in os.listdir(self.material_folder_path):
                        if file.endswith('_已检索.xlsx'):
                            indexed_files.append(os.path.join(self.material_folder_path, file))

                    if indexed_files:
                        self.add_log(f"找到 {len(indexed_files)} 个已检索的Excel文件，正在加载...")

                        # 创建材质任务列表
                        self.material_tasks = []

                        # 处理每个已检索的Excel文件
                        for excel_file in indexed_files:
                            try:
                                # 提取材质名称
                                base_name = os.path.basename(excel_file)
                                material_name = os.path.splitext(base_name)[0].replace('_已检索', '')

                                # 读取Excel文件中的所有工作表
                                xls = pd.ExcelFile(excel_file)
                                sheet_names = xls.sheet_names

                                # 为每个工作表创建任务
                                for sheet_name in sheet_names:
                                    task = {
                                        'material_name': material_name,
                                        'excel_file': excel_file.replace('_已检索', ''),  # 原始Excel文件路径
                                        'sheet_name': sheet_name,
                                        'status': 'success',  # 已经检索成功
                                        'message': '从已检索文件加载'
                                    }
                                    self.material_tasks.append(task)
                                    self.add_log(f"已加载任务: {material_name} - {sheet_name}")
                            except Exception as e:
                                self.add_log(f"加载已检索文件 {excel_file} 失败: {str(e)}")
                    else:
                        self.add_log("错误: 未找到已检索的Excel文件，请先执行图片索引")
                        QMessageBox.warning(self, "警告", "未找到已检索的Excel文件，请先执行图片索引")
                        return
                else:
                    self.add_log("错误: 请先检索图片")
                    QMessageBox.warning(self, "警告", "请先检索图片")
                    return

            # 再次检查是否有材质任务
            if not self.material_tasks:
                self.add_log("错误: 无法加载任何材质任务")
                QMessageBox.warning(self, "警告", "无法加载任何材质任务")
                return

            # 检查是否有正在运行的任务
            if self.layout_worker and self.layout_worker.isRunning():
                reply = self.show_notification(
                    "任务正在运行",
                    "当前有布局任务正在运行，是否停止并重新开始?",
                    "question"
                )
                if reply == QMessageBox.StandardButton.Yes:
                    self.layout_worker.stop()
                    self.layout_worker.wait()
                else:
                    return

            # 准备布局任务
            self.prepare_layout_tasks()

            # 开始执行第一个任务
            self.execute_next_material_task()

        except Exception as e:
            self.add_log(f"启动布局任务时出错: {str(e)}")
            QMessageBox.warning(self, "错误", f"启动布局任务时出错: {str(e)}")

    def prepare_layout_tasks(self):
        """准备布局任务"""
        try:
            self.add_log("准备布局任务...")

            # 重置任务状态
            for task in self.material_tasks:
                if task['status'] == 'success':  # 只处理检索成功的任务
                    task['status'] = 'pending'
                    task['message'] = ''

            # 更新UI状态
            self.progress_bar.setValue(0)
            self.status_label.setText("状态: 正在准备布局任务...")

            # 获取待处理任务数量
            pending_tasks = sum(1 for task in self.material_tasks if task['status'] == 'pending')
            self.add_log(f"找到 {pending_tasks} 个待处理任务")

        except Exception as e:
            self.add_log(f"准备布局任务时出错: {str(e)}")
            raise

    def execute_next_material_task(self):
        """执行下一个材质任务"""
        try:
            # 找到下一个待处理的任务
            next_task = None
            for task in self.material_tasks:
                if task['status'] == 'pending':
                    next_task = task
                    break

            if not next_task:
                self.add_log("所有材质任务已完成")
                self.status_label.setText("状态: 所有任务已完成")
                # 确保进度条显示100%
                self.update_progress(100)
                return

            # 更新当前任务状态
            next_task['status'] = 'processing'
            self.current_task = next_task

            self.add_log(f"开始处理任务: {next_task['material_name']} - {next_task['sheet_name']}")

            # 读取Excel数据
            # 使用带有 "_已检索" 后缀的处理后的Excel文件
            base_excel_file = next_task['excel_file']
            processed_excel_file = os.path.join(
                os.path.dirname(base_excel_file),
                f"{os.path.splitext(os.path.basename(base_excel_file))[0]}_已检索.xlsx"
            )
            sheet_name = next_task['sheet_name']

            # 检查处理后的文件是否存在
            if not os.path.exists(processed_excel_file):
                self.add_log(f"错误: 已处理的文件不存在 {processed_excel_file}")
                next_task['status'] = 'failed'
                next_task['message'] = "已处理的文件不存在"
                QTimer.singleShot(100, self.execute_next_material_task)
                return

            try:
                # 读取处理后的Excel文件
                df = pd.read_excel(processed_excel_file, sheet_name=sheet_name)

                # 确保关键列存在 - 检查不同可能的列名
                image_path_col = None
                for col_name in ['图片路径', '图像路径', 'image_path', '路径', 'path']:
                    if col_name in df.columns:
                        image_path_col = col_name
                        break

                if image_path_col is None:
                    # 输出所有列名以便于调试
                    self.add_log(f"错误: 在 {sheet_name} 中找不到图片路径列，可用列: {', '.join(df.columns)}")
                    next_task['status'] = 'failed'
                    next_task['message'] = "找不到图片路径列"
                    QTimer.singleShot(100, self.execute_next_material_task)
                    return

                # 获取测试模式设置
                test_mode_settings = self.config_manager.get_test_mode_settings()
                is_test_mode = test_mode_settings.get('is_test_mode', False)
                is_test_all_data = test_mode_settings.get('is_test_all_data', False)

                # 如果是测试模式且开启了测试全部数据，则不过滤"未入库"的行
                if is_test_mode and is_test_all_data:
                    self.add_log(f"测试全部数据模式: 保留所有图片数据，包括'未入库'的图片")
                else:
                    # 过滤掉"未入库"的行
                    df = df[df[image_path_col] != '未入库']

                if df.empty:
                    self.add_log(f"警告: {sheet_name} 中没有可用的图片")
                    next_task['status'] = 'failed'
                    next_task['message'] = "没有可用的图片"
                    QTimer.singleShot(100, self.execute_next_material_task)
                    return

                # 检查宽度和高度列
                width_col = None
                for col_name in ['宽度', '宽', 'width', 'Width', '宽cm', 'width_cm']:
                    if col_name in df.columns:
                        width_col = col_name
                        break

                height_col = None
                for col_name in ['高度', '高', 'height', 'Height', '高cm', 'height_cm']:
                    if col_name in df.columns:
                        height_col = col_name
                        break

                if width_col is None or height_col is None:
                    self.add_log(f"错误: 在 {sheet_name} 中找不到宽度或高度列，可用列: {', '.join(df.columns)}")
                    next_task['status'] = 'failed'
                    next_task['message'] = "找不到宽度或高度列"
                    QTimer.singleShot(100, self.execute_next_material_task)
                    return

                # 检查图案名称列
                pattern_name_col = None
                for col_name in ['图案全称', '图案', '名称', 'name', 'Name', 'pattern_name', '图案名称']:
                    if col_name in df.columns:
                        pattern_name_col = col_name
                        break

                if pattern_name_col is None:
                    self.add_log(f"错误: 在 {sheet_name} 中找不到图案名称列，可用列: {', '.join(df.columns)}")
                    next_task['status'] = 'failed'
                    next_task['message'] = "找不到图案名称列"
                    QTimer.singleShot(100, self.execute_next_material_task)
                    return

                # 检查数量列
                quantity_col = None
                for col_name in ['数量', 'quantity', 'Quantity']:
                    if col_name in df.columns:
                        quantity_col = col_name
                        break

                # 准备图片项
                pattern_items = []
                for _, row in df.iterrows():
                    # 提取数量信息
                    quantity = 1  # 默认数量为1
                    if quantity_col and quantity_col in row and pd.notna(row[quantity_col]):
                        try:
                            quantity = int(float(row[quantity_col]))
                        except (ValueError, TypeError):
                            quantity = 1

                    # 确保数量至少为1
                    quantity = max(1, quantity)

                    # 创建图案项 - 优先使用图案全称，如果为空则使用图案名称
                    pattern_name = "未命名图案"

                    # 首先尝试使用选定的pattern_name_col
                    if pd.notna(row[pattern_name_col]):
                        # 确保pattern_name是字符串类型
                        pattern_name = str(row[pattern_name_col]).strip()

                    # 如果pattern_name仍然是"未命名图案"，尝试使用其他可能的列
                    if pattern_name == "未命名图案":
                        # 如果当前使用的是"图案"列，尝试使用"图案全称"列
                        if pattern_name_col == "图案" and "图案全称" in df.columns and pd.notna(row["图案全称"]):
                            pattern_name = str(row["图案全称"]).strip()
                        # 如果当前使用的是"图案全称"列，尝试使用"图案"列
                        elif pattern_name_col == "图案全称" and "图案" in df.columns and pd.notna(row["图案"]):
                            pattern_name = str(row["图案"]).strip()

                    # 记录使用的图案名称来源
                    if pattern_name != "未命名图案":
                        self.add_log(f"使用图案名称: {pattern_name}")

                    try:
                        width = float(row[width_col])
                        height = float(row[height_col])
                    except (ValueError, TypeError):
                        self.add_log(f"警告: 跳过无效尺寸的行: {pattern_name}")
                        continue

                    pattern_items.append({
                        'path': row[image_path_col],
                        'width_cm': width,
                        'height_cm': height,
                        'pattern_name': pattern_name,
                        'quantity': quantity
                    })

                if not pattern_items:
                    self.add_log(f"警告: 无法提取有效的图片信息")
                    next_task['status'] = 'failed'
                    next_task['message'] = "无法提取有效的图片信息"
                    QTimer.singleShot(100, self.execute_next_material_task)
                    return

                # 处理特定图案列表
                self._process_pattern_batch(next_task, sheet_name, pattern_items)

            except Exception as e:
                self.add_log(f"处理Excel文件失败: {str(e)}")
                next_task['status'] = 'failed'
                next_task['message'] = f"处理失败: {str(e)}"
                QTimer.singleShot(100, self.execute_next_material_task)

        except Exception as e:
            self.add_log(f"执行材质任务时出错: {str(e)}")
            if self.current_task:
                self.current_task['status'] = 'failed'
                self.current_task['message'] = str(e)
            QTimer.singleShot(100, self.execute_next_material_task)

    def _process_pattern_batch(self, task, sheet_name, pattern_items, sequence_start=1):
        """处理一批图案，可能会根据最大高度分多次处理"""
        try:
            # 从sheet名称中提取宽度信息
            width_from_sheet = None

            # 尝试匹配数字作为宽度，假设格式为"名称-宽度-其他"或"名称-宽度"或纯数字
            width_matches = re.findall(r'(\d+)', sheet_name)
            if width_matches:
                # 使用找到的第一个数字作为宽度
                try:
                    width_from_sheet = int(width_matches[0])
                    self.add_log(f"从sheet名称 '{sheet_name}' 中提取到宽度: {width_from_sheet}厘米")
                except (ValueError, IndexError):
                    width_from_sheet = None

            # 如果无法从sheet名称中提取宽度，则使用默认宽度
            canvas_width_int = width_from_sheet if width_from_sheet else int(self.canvas_width_m * 100)

            # 计算包含水平拓展的宽度（用于实际画布创建，但不用于文件命名）
            canvas_width_with_expansion_int = canvas_width_int + int(self.horizontal_expansion_cm)
            self.add_log(f"画布宽度: {canvas_width_int}厘米 + 水平拓展: {self.horizontal_expansion_cm}厘米 = {canvas_width_with_expansion_int}厘米")

            # 设置当前任务的画布宽度（以米为单位）
            current_canvas_width_m = canvas_width_int / 100

            # 创建输出路径 - 使用材质-宽幅-序号格式（宽幅不包含水平拓展）
            canvas_sequence = sequence_start  # 使用指定的起始序号

            # 检查已有的输出文件，确定序号
            output_dir = self.material_folder_path  # 输出到操作表格目录
            # 使用不包含水平拓展的宽度作为前缀
            prefix = f"{task['material_name']}-{canvas_width_int}-"
            existing_files = [f for f in os.listdir(output_dir) if f.startswith(prefix) and f.endswith('.tiff')]
            if existing_files:
                # 如果有已存在的文件，找出最大序号并加1
                try:
                    existing_sequences = []
                    for filename in existing_files:
                        seq_str = filename.replace(prefix, '').replace('.tiff', '')
                        if seq_str.isdigit():
                            existing_sequences.append(int(seq_str))
                    if existing_sequences:
                        canvas_sequence = max(existing_sequences) + 1
                except Exception as e:
                    self.add_log(f"确定序号时出错: {str(e)}")

            # 使用不包含水平拓展的宽度创建画布名称
            canvas_name = f"{task['material_name']}-{canvas_width_int}-{canvas_sequence}"

            # 强制使用RectPack算法（替换所有tetris算法）
            self.add_log("强制启用RectPack算法，替换所有tetris算法")

            # 使用RectPack统一布局工作器
            from ui.rectpack_layout_worker import RectPackLayoutWorker
            self.layout_worker = RectPackLayoutWorker(
                config_manager=self.config_manager,
                image_indexer=self.image_indexer,
                excel_processor=self.excel_processor
            )
            self.layout_worker.set_parameters(
                library_path=self.library_path,
                material_folder_path=self.material_folder_path,
                canvas_width_m=current_canvas_width_m,
                max_height_cm=self.max_height_cm,
                ppi=self.ppi,
                image_spacing_cm=self.image_spacing_cm,
                horizontal_expansion_cm=self.horizontal_expansion_cm
            )
            self.add_log("使用RectPack统一布局算法（已替换tetris算法）")
            self.add_log(f"RectPack参数: 画布宽度={current_canvas_width_m}m, 最大高度={self.max_height_cm}cm")
            self.add_log(f"图库路径: {self.library_path}")
            self.add_log(f"材质文件夹: {self.material_folder_path}")

            # RectPack工作器的参数已在创建时设置
            test_mode_settings = self.config_manager.get_test_mode_settings()
            if test_mode_settings['is_test_mode']:
                self.add_log(f"RectPack模式 - 测试模式已启用，统一单位处理")

            # 为RectPack工作器设置画布信息和图案数据
            self.layout_worker.set_canvas_info(
                canvas_name=canvas_name,
                material_name=task['material_name'],
                output_path=self.material_folder_path,
                canvas_sequence=canvas_sequence
            )
            # 设置图案数据，避免重复处理
            self.layout_worker.set_pattern_items(pattern_items)

            # 连接信号
            self.layout_worker.progress_signal.connect(self.update_progress)
            self.layout_worker.log_signal.connect(self.add_log)

            # RectPack工作器的信号连接
            self.layout_worker.finished_signal.connect(self.layout_task_finished)
            self.layout_worker.error_signal.connect(lambda msg: self.add_log(f"RectPack错误: {msg}"))
            # 连接新画布需求信号（RectPack也支持多画布）
            self.layout_worker.new_canvas_needed.connect(self._handle_new_canvas_needed)
            # RectPack工作器可能有stage_signal
            if hasattr(self.layout_worker, 'stage_signal'):
                self.layout_worker.stage_signal.connect(lambda stage, _, status: self.add_log(f"[{stage}] {status}"))

            # 更新进度指示器
            self.progress_indicator.set_title("排列图片")
            self.progress_indicator.set_status(f"正在排列 {canvas_name} 的图片...")
            self.update_progress(0, stage_index=3)  # 使用排列阶段

            # 启动任务
            self.layout_worker.start()
            self.add_log(f"开始布局任务: {canvas_name}")

        except Exception as e:
            self.add_log(f"处理图案批次时出错: {str(e)}")
            if self.current_task:
                self.current_task['status'] = 'failed'
                self.current_task['message'] = str(e)
            QTimer.singleShot(100, self.execute_next_material_task)

    def _handle_new_canvas_needed(self, material_name, remaining_patterns):
        """处理需要新画布的情况"""
        try:
            # 确保当前任务还在处理中
            if not self.current_task or self.current_task['material_name'] != material_name:
                self.add_log(f"警告: 收到新画布需求，但当前没有匹配的任务 ({material_name})")
                return

            # 记录当前进度
            self.add_log(f"准备为 {material_name} 创建新画布，处理剩余 {len(remaining_patterns)} 个图案")

            # 分析剩余图片的分类情况
            a_class_count = sum(1 for p in remaining_patterns if p.get('image_class') == 'A')
            b_class_count = sum(1 for p in remaining_patterns if p.get('image_class') == 'B')
            c_class_count = sum(1 for p in remaining_patterns if p.get('image_class') == 'C')
            self.add_log(f"剩余图片分类: A类={a_class_count}个, B类={b_class_count}个, C类={c_class_count}个")

            # 查找下一个可用序号
            # 由于当前的画布处理完成后，下一个序号应该是当前序号+1
            next_sequence = self.layout_worker.canvas_sequence + 1
            sheet_name = self.current_task['sheet_name']

            # 优化C类图片分配 - 避免个位数图片分到一个画布
            if len(remaining_patterns) < 10:
                self.add_log(f"检测到剩余图片数量较少 ({len(remaining_patterns)} < 10)，尝试优化分配...")

                # 获取当前画布上的图片数量
                current_canvas_images = 0
                if hasattr(self.layout_worker, 'images_arranged'):
                    current_canvas_images = self.layout_worker.images_arranged

                # 如果当前画布图片数量足够多，且剩余图片较少，则尝试将剩余图片放到当前画布
                if current_canvas_images >= 20 and len(remaining_patterns) < 5:
                    self.add_log(f"当前画布已有 {current_canvas_images} 个图片，剩余图片较少，尝试强制放入当前画布")
                    # 设置强制放入标志
                    if hasattr(self.layout_worker, 'force_fit_remaining'):
                        self.layout_worker.force_fit_remaining = True
                        # 重新尝试放置剩余图片
                        self.layout_worker.retry_remaining_patterns(remaining_patterns)
                        return

                # 如果剩余图片数量较少，但不能放入当前画布，则检查是否有下一个画布
                # 如果有下一个画布，则尝试平衡分配
                if next_sequence > 1 and len(remaining_patterns) < 10:
                    # 获取当前所有画布的图片数量
                    canvas_counts = {}
                    for task in self.material_tasks:
                        if task['status'] == 'completed' and task['material_name'] == material_name:
                            canvas_seq = task.get('canvas_sequence', 0)
                            if canvas_seq > 0:
                                canvas_counts[canvas_seq] = task.get('images_arranged', 0)

                    # 如果有多个画布，尝试平衡最后几个画布的图片数量
                    if len(canvas_counts) > 1:
                        self.add_log(f"尝试平衡最后几个画布的图片数量...")
                        # 这里可以实现更复杂的平衡算法
                        # 但为了简单起见，我们只是记录日志

            # 确保每个图片都有唯一标识符和分类信息
            for i, pattern in enumerate(remaining_patterns):
                # 添加唯一标识符
                if 'unique_id' not in pattern:
                    width_cm = pattern.get('width_cm', 0)
                    height_cm = pattern.get('height_cm', 0)
                    pattern_name = pattern.get('pattern_name', f'图片{i+1}')
                    pattern['unique_id'] = f"{pattern_name}_{width_cm:.1f}_{height_cm:.1f}_{i}"
                    self.add_log(f"为剩余图片添加唯一标识符: {pattern['unique_id']}")

                # 确保有分类信息
                if 'image_class' not in pattern:
                    # 根据已有的分类信息推断
                    if pattern.get('unique_id', '').startswith('A_') or pattern.get('unique_id', '').endswith('_A'):
                        pattern['image_class'] = 'A'
                    elif pattern.get('unique_id', '').startswith('B_') or pattern.get('unique_id', '').endswith('_B'):
                        pattern['image_class'] = 'B'
                    else:
                        pattern['image_class'] = 'C'
                    self.add_log(f"为剩余图片添加分类信息: {pattern.get('pattern_name', '')} -> {pattern['image_class']}类")

            # 创建新的处理任务
            QTimer.singleShot(500, lambda: self._process_pattern_batch(
                self.current_task,
                sheet_name,
                remaining_patterns,
                next_sequence
            ))

        except Exception as e:
            self.add_log(f"处理新画布需求时出错: {str(e)}")

    def layout_task_finished(self):
        """布局任务完成的处理"""
        try:
            if not self.current_task:
                return

            # 检查任务是否成功完成
            success = False
            if hasattr(self, 'layout_worker') and hasattr(self.layout_worker, 'success'):
                success = self.layout_worker.success

            # 检查是否有剩余图案需要处理（仅对传统布局工作器）
            has_remaining = False
            if hasattr(self, 'layout_worker') and hasattr(self.layout_worker, 'remaining_patterns'):
                has_remaining = len(self.layout_worker.remaining_patterns) > 0

            # 只有当没有剩余图案时，才更新任务状态为完成
            if not has_remaining:
                # 更新任务状态
                if success:
                    self.current_task['status'] = 'completed'
                    self.add_log(f"任务 {self.current_task['material_name']} 完成")
                else:
                    self.current_task['status'] = 'failed'
                    self.add_log(f"任务 {self.current_task['material_name']} 失败")

                # 计算总进度
                total_tasks = sum(1 for task in self.material_tasks
                                if task['status'] in ['pending', 'processing', 'completed', 'failed'])
                completed_tasks = sum(1 for task in self.material_tasks
                                    if task['status'] in ['completed', 'failed'])

                if total_tasks > 0:
                    # 检查是否所有任务都已完成
                    if completed_tasks == total_tasks:
                        # 所有任务已完成，设置进度为100%
                        self.update_progress(100)
                        self.status_label.setText("状态: 所有任务已完成")
                    else:
                        # 还有未完成的任务，按比例计算进度
                        progress = int(completed_tasks / total_tasks * 100)
                        self.update_progress(progress)

                # 处理下一个任务
                QTimer.singleShot(100, self.execute_next_material_task)

        except Exception as e:
            self.add_log(f"处理布局任务完成事件时出错: {str(e)}")
            QTimer.singleShot(100, self.execute_next_material_task)

    def _handle_status_update(self, status_type, status_data):
        """处理状态更新信号

        Args:
            status_type: 状态类型，如'progress', 'stats', 'complete'等
            status_data: 状态数据字典
        """
        try:
            if status_type == 'progress':
                # 处理进度更新
                if 'percent' in status_data:
                    # 获取已处理和总项数
                    items_processed = status_data.get('items_processed', None)
                    total_items = status_data.get('total_items', None)

                    # 更新进度
                    self.update_progress(status_data['percent'], stage_index=3,
                                        items_processed=items_processed,
                                        total_items=total_items)

            elif status_type == 'stats':
                # 处理统计信息
                if 'layout_info' in status_data and 'utilization' in status_data['layout_info']:
                    utilization = status_data['layout_info']['utilization'] * 100
                    self.status_label.setText(f"状态: 布局中 - 利用率 {utilization:.2f}%")

                    # 更新进度指示器状态
                    self.progress_indicator.set_status(f"利用率: {utilization:.2f}%")

            elif status_type == 'complete':
                # 处理完成状态
                if 'output_path' in status_data:
                    output_file = os.path.basename(status_data['output_path'])
                    self.status_label.setText(f"状态: 已完成 - 输出到 {output_file}")

                    # 更新进度指示器
                    self.progress_indicator.set_title("已完成")
                    self.progress_indicator.set_status(f"已输出到 {output_file}")
                    self.progress_indicator.start_stage(4)  # 切换到输出阶段
                    self.progress_indicator.update_progress(1.0)  # 设置为100%

                    # 完成所有阶段
                    QTimer.singleShot(1000, self.progress_indicator.complete)

        except Exception as e:
            self.add_log(f"处理状态更新时出错: {str(e)}")
            log.error(f"处理状态更新时出错: {str(e)}")

    def show_notification(self, title, message, message_type="info", buttons=None, parent=None):
        """显示统一的弹窗通知

        Args:
            title: 弹窗标题
            message: 弹窗消息内容
            message_type: 消息类型，可选值为"info", "warning", "error", "question"
            buttons: 自定义按钮，默认为None（使用标准按钮）
            parent: 父窗口，默认为self

        Returns:
            用户的选择结果
        """
        # 如果未指定父窗口，使用self
        if parent is None:
            parent = self

        # 创建消息框
        msg_box = QMessageBox(parent)
        msg_box.setWindowTitle(title)
        msg_box.setText(message)

        # 根据消息类型设置图标和按钮
        if message_type == "info":
            msg_box.setIcon(QMessageBox.Icon.Information)
            if buttons is None:
                buttons = QMessageBox.StandardButton.Ok
        elif message_type == "warning":
            msg_box.setIcon(QMessageBox.Icon.Warning)
            if buttons is None:
                buttons = QMessageBox.StandardButton.Ok
        elif message_type == "error":
            msg_box.setIcon(QMessageBox.Icon.Critical)
            if buttons is None:
                buttons = QMessageBox.StandardButton.Ok
        elif message_type == "question":
            msg_box.setIcon(QMessageBox.Icon.Question)
            if buttons is None:
                buttons = QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
        else:
            # 默认使用信息图标
            msg_box.setIcon(QMessageBox.Icon.Information)
            if buttons is None:
                buttons = QMessageBox.StandardButton.Ok

        # 设置按钮
        msg_box.setStandardButtons(buttons)

        # 显示消息框并返回结果
        return msg_box.exec()

    # 已移除重复的add_log方法定义，使用第67行的版本

    def check_version(self):
        """检查应用版本并更新窗口标题"""
        # 设置默认标题
        app_title = APP_NAME

        if self.supabase_helper.is_connected():
            try:
                # 尝试获取配置，先检查用户是否已登录
                if self.supabase_helper.is_authenticated():
                    log.info("用户已登录，使用已认证客户端获取配置")
                else:
                    log.info("用户未登录，将尝试使用匿名客户端获取配置")

                config = self.supabase_helper.fetch_config()
                if not config:
                    log.error("未能获取云端配置信息")
                    # 即使获取失败也设置默认标题
                    self.setWindowTitle(app_title)

                    # 如果用户已登录但仍然获取失败，可能是权限问题
                    if self.supabase_helper.is_authenticated():
                        QMessageBox.warning(
                            self,
                            "配置获取警告",
                            "已登录但无法获取云端配置，可能是权限问题，将使用默认配置"
                        )
                    else:
                        QMessageBox.warning(
                            self,
                            "配置获取警告",
                            "无法获取云端配置，将使用默认配置"
                        )
                    return  # 使用默认配置继续运行，而不是退出

                # 成功获取配置
                latest_version = config.get('app_ver')
                if not latest_version:
                    log.warning("云端配置中没有版本信息，将使用当前版本")
                    latest_version = APP_VERSION

                log.info(f"当前应用版本: {APP_VERSION}, 云端版本: {latest_version}")
                if APP_VERSION != latest_version:
                    msg = QMessageBox()
                    msg.setIcon(QMessageBox.Icon.Warning)  # 改为警告而不是错误
                    msg.setText("版本不匹配")
                    msg.setInformativeText(f"当前版本 {APP_VERSION} 与云端版本 {latest_version} 不匹配，建议升级")
                    msg.setWindowTitle("版本提醒")
                    msg.exec()
                    # 不强制退出，允许用户继续使用

                # 更新应用标题
                latest_title = config.get('app_title')
                if latest_title:
                    app_title = latest_title + '-v' + latest_version

                # 添加用户邮箱到标题
                if self.supabase_helper.is_authenticated():
                    user_email = self.supabase_helper.get_user_email()
                    if user_email:
                        app_title = f"{app_title} - {user_email}"
                        log.info(f"已将用户邮箱 {user_email} 添加到窗口标题")

                self.setWindowTitle(app_title)

            except Exception as e:
                log.error(f"云端配置检查失败: {type(e).__name__}: {str(e)}")
                # 异常情况下也设置默认标题
                self.setWindowTitle(APP_NAME)
                QMessageBox.warning(
                    self,
                    "配置检查警告",
                    f"获取云端配置时出现异常，将使用默认配置"
                )
                # 不强制退出，允许用户继续使用

    def update_progress(self, value, stage_index=None, items_processed=None, total_items=None):
        """
        更新进度条和进度指示器

        Args:
            value: 进度值（0-100）
            stage_index: 阶段索引，如果为None则使用当前阶段
            items_processed: 已处理项数
            total_items: 总项数
        """
        try:
            # 确保value是整数
            value = int(value)
            if value < 0:
                value = 0
            elif value > 100:
                value = 100

            # 更新旧的进度条（兼容性）
            self.progress_bar.setValue(value)

            # 更新新的进度指示器
            if hasattr(self, 'progress_indicator'):
                # 如果指定了阶段索引，切换到该阶段
                if stage_index is not None and 0 <= stage_index < 5:  # 我们有5个阶段
                    self.progress_indicator.start_stage(stage_index)

                # 更新进度
                progress = value / 100.0  # 转换为0-1范围
                self.progress_indicator.update_progress(progress, items_processed, total_items)

                # 如果进度为100%，设置为不确定状态
                if value == 100:
                    self.progress_indicator.set_indeterminate(True)
                elif value == 0:
                    # 如果进度为0%，重置为确定状态
                    self.progress_indicator.set_indeterminate(False)

            # 更新UI
            QApplication.processEvents()

        except Exception as e:
            self.add_log(f"更新进度条时出错: {str(e)}")
            log.error(f"更新进度条时出错: {str(e)}")

    def set_buttons_enabled(self):
        """设置按钮状态"""
        # 图库按钮
        has_library = hasattr(self, 'library_path') and os.path.exists(self.library_path)
        self.index_btn.setEnabled(has_library)

        # 材质信息按钮
        has_material = hasattr(self, 'material_folder_path') and os.path.exists(self.material_folder_path)
        self.load_and_layout_btn.setEnabled(has_material)
        self.image_index_btn.setEnabled(has_material)  # 图片索引按钮状态与智能排版相同

        # 设置按钮始终可用
        self.settings_btn.setEnabled(True)

    def update_buttons_layout(self):
        """根据模糊查询设置更新按钮布局"""
        # 清空现有布局
        layout = self.layout_buttons_container.layout()
        while layout.count():
            item = layout.takeAt(0)
            if item.widget():
                item.widget().setParent(None)

        # 根据模糊查询设置决定按钮布局
        is_fuzzy_query = self.config_manager.get_fuzzy_query()
        if is_fuzzy_query:
            # 如果开启模糊查询，显示两个按钮，并更新按钮文本
            self.load_and_layout_btn.setText("图片排版")
            layout.addWidget(self.image_index_btn)
            layout.addWidget(self.load_and_layout_btn)
            self.image_index_btn.setVisible(True)
            self.add_log("模糊查询模式已开启，界面已更新为两阶段模式")
        else:
            # 如果关闭模糊查询，只显示智能排版按钮，并更新按钮文本
            self.load_and_layout_btn.setText("智能排版")
            layout.addWidget(self.load_and_layout_btn)
            self.image_index_btn.setVisible(False)
            self.add_log("模糊查询模式已关闭，界面已更新为单阶段模式")



    def on_index_library_finished(self, success, message, count):
        """索引图库完成的回调函数

        Args:
            success: 是否成功
            message: 消息
            count: 图片数量
        """
        # 重新启用按钮
        self.index_btn.setEnabled(True)
        self.library_btn.setEnabled(True)

        if success:
            self.add_log(f"图库索引完成: {count} 个图片")
            self.status_label.setText("图库索引完成")
            self.progress_bar.setValue(100)
        else:
            self.add_log(f"索引失败: {message}")
            self.status_label.setText("图库索引失败")
            QMessageBox.warning(self, "错误", message)

        # 清理工作线程
        self.index_library_worker = None

    def on_retrieve_images_finished(self, success, message, tasks):
        """检索图片完成的回调函数

        Args:
            success: 是否成功
            message: 消息
            tasks: 任务列表
        """
        # 重新启用按钮
        self.load_and_layout_btn.setEnabled(True)
        self.material_btn.setEnabled(True)

        if success:
            # 更新材质任务列表
            self.material_tasks = tasks

            # 统计结果
            total_tasks = len(tasks)
            success_tasks = sum(1 for task in tasks if task['status'] == 'success')
            failed_tasks = sum(1 for task in tasks if task['status'] == 'failed')

            # 显示详细统计信息
            self.add_log(f"材质任务处理完成:")
            self.add_log(f"- 总任务数: {total_tasks}")
            self.add_log(f"- 成功任务: {success_tasks}")
            self.add_log(f"- 失败任务: {failed_tasks}")

            # 显示每个任务的状态
            for task in tasks:
                status_icon = "✓" if task['status'] == 'success' else "✗"
                self.add_log(f"{status_icon} {task['material_name']} - {task['sheet_name']}: {task['message']}")

            # 根据模糊查询设置和自动排版标志决定提示信息
            is_fuzzy_query = self.config_manager.get_fuzzy_query()
            if is_fuzzy_query and not (hasattr(self, '_auto_start_layout') and self._auto_start_layout):
                # 模糊查询模式下的图片索引完成
                self.status_label.setText("图片索引完成，可以点击'图片排版'按钮进行排版")
                self.add_log("图片索引完成，已生成已检索表格，可以点击'图片排版'按钮进行排版")
                # 显示成功消息框
                self.show_notification(
                    "图片索引完成",
                    "图片索引已完成，已生成已检索表格。\n\n请点击'图片排版'按钮进行排版。",
                    "info"
                )
            else:
                # 普通模式或自动排版
                self.status_label.setText("检索完成")

            self.progress_bar.setValue(100)

            # 如果是从检索并排列功能调用的，自动开始排列
            if hasattr(self, '_auto_start_layout') and self._auto_start_layout:
                self._auto_start_layout = False
                QTimer.singleShot(500, self.start_layout)
        else:
            self.add_log(f"检索失败: {message}")
            self.status_label.setText("检索失败")
            QMessageBox.warning(self, "错误", message)

        # 清理工作线程
        self.retrieve_images_worker = None

    def closeEvent(self, event):
        """应用关闭时的处理"""
        try:
            # 停止定时器
            if hasattr(self, 'log_update_timer'):
                self.log_update_timer.stop()

            # 创建一个定时器，确保即使线程无法正常停止，应用程序也能在超时后关闭
            close_timer = QTimer(self)
            close_timer.setSingleShot(True)
            close_timer.timeout.connect(lambda: self._force_close(event))
            close_timer.start(3000)  # 3秒超时

            # 开始关闭处理
            self._start_closing_sequence(event, close_timer)

        except Exception as e:
            log.error(f"关闭应用时出错: {str(e)}")
            # 强制关闭
            super().closeEvent(event)

    def _start_closing_sequence(self, event, close_timer):
        """开始关闭序列，非阻塞方式关闭线程"""
        try:
            # 停止所有工作线程，使用非阻塞方式
            threads_to_stop = []

            if hasattr(self, 'index_library_worker') and self.index_library_worker:
                self.index_library_worker.stop()
                threads_to_stop.append(self.index_library_worker)

            if hasattr(self, 'retrieve_images_worker') and self.retrieve_images_worker:
                self.retrieve_images_worker.stop()
                threads_to_stop.append(self.retrieve_images_worker)

            if hasattr(self, 'layout_worker') and self.layout_worker and self.layout_worker.isRunning():
                self.layout_worker.stop()
                threads_to_stop.append(self.layout_worker)

            # 如果没有线程需要停止，直接完成关闭
            if not threads_to_stop:
                self._finish_closing(event, close_timer)
                return

            # 创建一个定时器来检查线程是否已经停止
            check_timer = QTimer(self)
            check_timer.setSingleShot(False)
            check_timer.timeout.connect(lambda: self._check_threads_stopped(threads_to_stop, event, close_timer, check_timer))
            check_timer.start(100)  # 每100毫秒检查一次

        except Exception as e:
            log.error(f"开始关闭序列时出错: {str(e)}")
            self._finish_closing(event, close_timer)

    def _check_threads_stopped(self, threads, event, close_timer, check_timer):
        """检查线程是否已经停止"""
        try:
            # 检查所有线程是否已经停止
            all_stopped = True
            for thread in threads:
                if thread.isRunning():
                    all_stopped = False
                    break

            if all_stopped:
                # 所有线程已停止，停止检查定时器并完成关闭
                check_timer.stop()
                self._finish_closing(event, close_timer)

        except Exception as e:
            log.error(f"检查线程停止状态时出错: {str(e)}")
            check_timer.stop()
            self._finish_closing(event, close_timer)

    def _finish_closing(self, event, close_timer):
        """完成关闭操作"""
        try:
            # 停止超时定时器
            close_timer.stop()

            # 退出Supabase账号
            if hasattr(self, 'supabase_helper') and self.supabase_helper:
                try:
                    if self.supabase_helper.is_authenticated():
                        success, message = self.supabase_helper.logout()
                        if success:
                            self.add_log("已成功退出Supabase账号")
                        else:
                            log.warning(f"退出Supabase账号失败: {message}")
                except Exception as e:
                    log.error(f"退出Supabase账号时出错: {str(e)}")

            # 关闭数据库连接
            if hasattr(self, 'config_manager'):
                self.config_manager.close()

            if hasattr(self, 'image_indexer') and self.image_indexer.db:
                self.image_indexer.db.close()

            # 清理Photoshop资源（仅在非测试模式下）
            try:
                # 检查是否处于测试模式
                test_mode_settings = self.config_manager.get_test_mode_settings()
                is_test_mode = test_mode_settings.get('is_test_mode', False)

                if not is_test_mode:
                    # 只有在非测试模式下才清理Photoshop资源
                    from utils.photoshop_helper import PhotoshopHelper
                    # 使用安全清理函数
                    PhotoshopHelper.safe_cleanup_resources()
                    log.info("已清理Photoshop资源")
                else:
                    log.info("测试模式下，跳过Photoshop资源清理")
            except Exception as e:
                log.error(f"清理Photoshop资源时出错: {str(e)}")

            # 继续正常关闭
            super().closeEvent(event)

        except Exception as e:
            log.error(f"完成关闭操作时出错: {str(e)}")
            # 强制关闭
            super().closeEvent(event)

    def _force_close(self, event):
        """强制关闭应用程序"""
        log.warning("关闭超时，强制关闭应用程序")

        # 尝试终止所有线程
        try:
            if hasattr(self, 'layout_worker') and self.layout_worker and self.layout_worker.isRunning():
                self.layout_worker.terminate()

            if hasattr(self, 'index_library_worker') and self.index_library_worker and self.index_library_worker.isRunning():
                self.index_library_worker.terminate()

            if hasattr(self, 'retrieve_images_worker') and self.retrieve_images_worker and self.retrieve_images_worker.isRunning():
                self.retrieve_images_worker.terminate()

            # 尝试退出Supabase账号（即使在强制关闭的情况下）
            if hasattr(self, 'supabase_helper') and self.supabase_helper:
                try:
                    if self.supabase_helper.is_authenticated():
                        self.supabase_helper.logout()
                        log.info("强制关闭时已退出Supabase账号")
                except Exception as e:
                    log.error(f"强制关闭时退出Supabase账号失败: {str(e)}")
        except Exception as e:
            log.error(f"终止线程时出错: {str(e)}")

        # 强制关闭
        super().closeEvent(event)

    def image_index_only(self):
        """仅执行图片索引功能（不进行排版）

        在模糊查询模式下，此功能用于从图库中搜索图片路径，生成"已检索表格"，
        但不执行排版操作。用户可以在索引完成后，通过"图片排版"按钮执行排版。
        """
        try:
            # 首先检查图库路径是否已设置
            if not self.library_path or not os.path.exists(self.library_path):
                self.add_log("错误: 未检索图库，请先选择图库路径并进行索引")
                QMessageBox.warning(self, "错误", "未检索图库，请先选择图库路径并进行索引")
                return

            # 检查图库是否已索引
            if not self.image_indexer.is_indexed(self.library_path):
                self.add_log("错误: 图库尚未索引，请先索引图库")
                QMessageBox.warning(self, "错误", "图库尚未索引，请先索引图库")
                return

            # 检查材质文件夹路径
            if not self.material_folder_path or not os.path.exists(self.material_folder_path):
                self.add_log("错误: 请先选择材质表格文件夹")
                QMessageBox.warning(self, "错误", "请先选择材质表格文件夹")
                return

            # 首先调用检索图片功能
            self.add_log("开始图片索引，将从图库中搜索图片路径，生成已检索表格...")

            # 设置标志，表示检索完成后不自动开始排列
            self._auto_start_layout = False

            # 执行检索图片操作
            self.load_material_info(start_layout=False)

        except Exception as e:
            self.add_log(f"图片索引时出错: {str(e)}")
            QMessageBox.warning(self, "错误", f"图片索引时出错: {str(e)}")

    def retrieve_and_layout(self):
        """检索图片并开始排列的组合功能"""
        try:
            # 根据模糊查询设置决定行为
            is_fuzzy_query = self.config_manager.get_fuzzy_query()

            if is_fuzzy_query:
                # 模糊查询开启时，智能排版按钮只执行排版阶段
                self.add_log("开始图片排版...")

                # 检查是否有材质文件夹
                if not hasattr(self, 'material_folder_path') or not os.path.exists(self.material_folder_path):
                    self.add_log("错误: 请先选择材质表格文件夹")
                    QMessageBox.warning(self, "警告", "请先选择材质表格文件夹")
                    return

                # 检查是否存在已检索的Excel文件
                has_indexed_files = False
                for file in os.listdir(self.material_folder_path):
                    if file.endswith('_已检索.xlsx'):
                        has_indexed_files = True
                        break

                if not has_indexed_files:
                    self.add_log("错误: 未找到已检索的Excel文件，请先执行图片索引")
                    QMessageBox.warning(self, "警告", "未找到已检索的Excel文件，请先执行图片索引")
                    return

                # 直接开始排版
                self.start_layout()
            else:
                # 模糊查询关闭时，保持原有逻辑
                self.add_log("开始检索图片并排列...")

                # 设置标志，表示检索完成后自动开始排列
                self._auto_start_layout = True

                # 执行检索图片操作
                self.load_material_info(start_layout=True)

        except Exception as e:
            self.add_log(f"检索并排列图片时出错: {str(e)}")
            QMessageBox.warning(self, "错误", f"检索并排列图片时出错: {str(e)}")

    def refresh_settings_password(self):
        """刷新高级设置密码"""
        try:
            # 仅后台刷新，不显示任何UI
            self.settings_auth.refresh_password()

            # 每60分钟刷新一次密码
            QTimer.singleShot(60 * 60 * 1000, self.refresh_settings_password)

        except Exception as e:
            log.error(f"刷新高级设置密码失败: {str(e)}")
            # 如果刷新失败，30分钟后重试
            QTimer.singleShot(30 * 60 * 1000, self.refresh_settings_password)

def check_login(supabase_helper):
    """检查用户登录状态，显示登录对话框

    Args:
        supabase_helper: Supabase辅助类实例

    Returns:
        是否登录成功
    """
    # 检查是否已认证
    if supabase_helper.is_authenticated():
        log.info("用户已登录，无需重新登录")
        return True

    # 显示登录对话框
    login_dialog = LoginDialog(supabase_helper, APP_NAME, APP_VERSION)
    if login_dialog.exec() == QDialog.DialogCode.Accepted:
        log.info("用户登录成功")
        return True
    else:
        log.info("用户取消登录")
        return False

if __name__ == "__main__":
    app = QApplication(sys.argv)

    # 初始化Supabase辅助类
    supabase_helper = SupabaseHelper()

    # 检查登录状态
    if check_login(supabase_helper):
        # 登录成功，显示主窗口
        window = ImageLayoutApp(supabase_helper)

        # 确保窗口标题包含用户邮箱
        window.check_version()

        window.show()
        sys.exit(app.exec())
    else:
        # 登录失败或取消，退出应用
        sys.exit(0)