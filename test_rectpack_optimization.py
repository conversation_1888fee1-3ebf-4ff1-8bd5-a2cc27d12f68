#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
RectPack算法优化功能测试脚本
验证所有核心功能是否正确实现
"""

import sys
import os

def test_unit_converter():
    """测试单位转换器"""
    print("=" * 50)
    print("测试1: 单位转换器")
    print("=" * 50)
    
    try:
        from utils.unit_converter import cm_to_px, px_to_cm, get_global_ppi
        print("✅ 单位转换器导入成功")
        
        # 测试转换
        test_cm = 120
        test_px = cm_to_px(test_cm)
        back_cm = px_to_cm(test_px)
        
        print(f"测试转换: {test_cm}cm -> {test_px}px -> {back_cm:.2f}cm")
        print(f"当前PPI: {get_global_ppi()}")
        
        # 验证转换精度
        if abs(back_cm - test_cm) < 0.1:
            print("✅ 转换精度测试通过")
        else:
            print("❌ 转换精度测试失败")
            
        return True
        
    except Exception as e:
        print(f"❌ 单位转换器测试失败: {e}")
        return False

def test_rectpack_test_mode():
    """测试RectPack测试模式"""
    print("\n" + "=" * 50)
    print("测试2: RectPack测试模式")
    print("=" * 50)
    
    try:
        from core.rectpack_test_mode import get_container_config_px, convert_pattern_items_to_px_data
        print("✅ RectPack测试模式导入成功")
        
        # 测试容器配置
        config = get_container_config_px(200, 2, 5000, 0.1)
        print(f"容器配置: 宽度{config['actual_width']}px, 最大高度{config['max_height']}px")
        
        # 验证容器配置
        expected_width = 200 + 2  # 基础宽度 + 水平拓展
        if config['actual_width'] == expected_width and config['max_height'] == 5000:
            print("✅ 容器配置测试通过")
        else:
            print("❌ 容器配置测试失败")
            
        # 测试图案数据转换
        pattern_items = [
            {'width_cm': 120, 'height_cm': 60, 'pattern_name': '测试图案1'},
            {'width_cm': 80, 'height_cm': 40, 'pattern_name': '测试图案2'}
        ]
        px_data = convert_pattern_items_to_px_data(pattern_items)
        print(f"图案转换: {len(pattern_items)} -> {len(px_data)} 张图片")
        
        # 验证转换结果
        if len(px_data) == 2 and px_data[0][0] == 120 and px_data[0][1] == 60:
            print("✅ 图案数据转换测试通过")
        else:
            print("❌ 图案数据转换测试失败")
            
        return True
        
    except Exception as e:
        print(f"❌ RectPack测试模式测试失败: {e}")
        return False

def test_config_manager():
    """测试配置管理器"""
    print("\n" + "=" * 50)
    print("测试3: 配置管理器")
    print("=" * 50)
    
    try:
        from utils.config_manager_duckdb import ConfigManagerDuckDB
        print("✅ 配置管理器导入成功")
        
        # 创建配置管理器实例
        config_manager = ConfigManagerDuckDB()
        
        # 测试测试模式设置
        test_mode_settings = config_manager.get_test_mode_settings()
        print(f"测试模式设置: {test_mode_settings}")
        
        # 验证设置结构
        required_keys = ['is_test_mode', 'is_test_all_data']
        if all(key in test_mode_settings for key in required_keys):
            print("✅ 测试模式设置结构正确")
        else:
            print("❌ 测试模式设置结构不完整")
            
        # 验证没有miniature_ratio
        if 'miniature_ratio' not in test_mode_settings:
            print("✅ miniature_ratio已成功移除")
        else:
            print("❌ miniature_ratio仍然存在")
            
        config_manager.close()
        return True
        
    except Exception as e:
        print(f"❌ 配置管理器测试失败: {e}")
        return False

def test_rectpack_layout_worker():
    """测试RectPack布局工作器"""
    print("\n" + "=" * 50)
    print("测试4: RectPack布局工作器")
    print("=" * 50)
    
    try:
        from ui.rectpack_layout_worker import RectPackLayoutWorker
        print("✅ RectPack布局工作器导入成功")
        
        # 创建工作器实例（不启动）
        worker = RectPackLayoutWorker()
        print("✅ RectPack布局工作器实例创建成功")
        
        # 检查关键方法是否存在
        required_methods = ['_create_test_mode_canvas_with_rectpack', 'set_parameters', 'set_canvas_info']
        missing_methods = []
        
        for method in required_methods:
            if not hasattr(worker, method):
                missing_methods.append(method)
                
        if not missing_methods:
            print("✅ RectPack布局工作器方法完整")
        else:
            print(f"❌ 缺少方法: {missing_methods}")
            
        return len(missing_methods) == 0
        
    except Exception as e:
        print(f"❌ RectPack布局工作器测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始RectPack算法优化功能测试")
    print("测试目标: 验证所有核心功能是否正确实现")
    
    # 运行所有测试
    tests = [
        ("单位转换器", test_unit_converter),
        ("RectPack测试模式", test_rectpack_test_mode),
        ("配置管理器", test_config_manager),
        ("RectPack布局工作器", test_rectpack_layout_worker),
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ 测试 {test_name} 执行异常: {e}")
            results.append((test_name, False))
    
    # 输出测试结果
    print("\n" + "=" * 50)
    print("测试结果汇总")
    print("=" * 50)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总体结果: {passed}/{total} 测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！RectPack算法优化功能正常")
        return True
    else:
        print("⚠️ 部分测试失败，需要进一步检查")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
